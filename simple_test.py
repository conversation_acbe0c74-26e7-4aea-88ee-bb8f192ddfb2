#!/usr/bin/env python3
"""
Simple test to check if AI-Feynman can be imported
"""

import sys
import os
sys.path.append('./AI-Feynman')

print("Testing AI-Feynman import...")

try:
    import aifeynman
    print("✓ Successfully imported aifeynman")
    print(f"Version: {aifeynman.__version__}")
    
    # Test the run_aifeynman function
    print("✓ run_aifeynman function available:", hasattr(aifeynman, 'run_aifeynman'))
    
    # Check if example data exists
    import os
    example_path = "./AI-Feynman/example_data/example1.txt"
    if os.path.exists(example_path):
        print("✓ Example data file exists")
        
        # Check file size
        file_size = os.path.getsize(example_path)
        print(f"✓ Example file size: {file_size} bytes")
        
        # Read first few lines
        with open(example_path, 'r') as f:
            lines = f.readlines()[:3]
            print(f"✓ First 3 lines of example data:")
            for i, line in enumerate(lines, 1):
                print(f"  Line {i}: {line.strip()}")
    else:
        print("✗ Example data file not found")
        
except Exception as e:
    print(f"✗ Error importing aifeynman: {e}")
    import traceback
    traceback.print_exc()

print("Test completed.")
