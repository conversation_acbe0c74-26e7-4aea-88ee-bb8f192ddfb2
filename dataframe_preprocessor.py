#!/usr/bin/env python3
"""
DataFrame Preprocessor for AI-Feynman

This module provides utilities to preprocess pandas DataFrames containing
numerical and categorical data to make them compatible with AI-Feynman.

AI-Feynman expects:
- Space-separated numerical values
- Each row represents one data point
- Each column represents one variable (including the target variable)
- All data must be numerical (no categorical data)

Author: AI Assistant
"""

import pandas as pd
import numpy as np
import os
import sys
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import LabelEncoder, StandardScaler, MinMaxScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error
from typing import List, Dict, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# Add AI-Feynman to path
sys.path.append('./AI-Feynman')
import a<PERSON>ynman


class DataFramePreprocessor:
    """
    Preprocessor class to prepare DataFrames for AI-Feynman symbolic regression.
    """
    
    def __init__(self, target_column: str, scale_features: bool = True, 
                 scaling_method: str = 'standard'):
        """
        Initialize the preprocessor.
        
        Args:
            target_column: Name of the target variable column
            scale_features: Whether to scale numerical features
            scaling_method: 'standard' for StandardScaler, 'minmax' for MinMaxScaler
        """
        self.target_column = target_column
        self.scale_features = scale_features
        self.scaling_method = scaling_method
        
        # Store encoders and scalers for inverse transformation
        self.label_encoders = {}
        self.feature_scaler = None
        self.target_scaler = None
        self.feature_columns = []
        self.categorical_columns = []
        self.numerical_columns = []
        self.preprocessor = None # Added to store ColumnTransformer
        
    def identify_column_types(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """
        Identify numerical and categorical columns in the DataFrame.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Dictionary with 'numerical' and 'categorical' column lists
        """
        numerical_cols = []
        categorical_cols = []
        
        for col in df.columns:
            if col == self.target_column:
                continue
                
            if df[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                # Check if it's actually categorical (few unique values)
                unique_ratio = df[col].nunique() / len(df)
                if unique_ratio < 0.05 and df[col].nunique() < 20:
                    categorical_cols.append(col)
                else:
                    numerical_cols.append(col)
            else:
                categorical_cols.append(col)
        
        self.numerical_columns = numerical_cols
        self.categorical_columns = categorical_cols
        
        return {
            'numerical': numerical_cols,
            'categorical': categorical_cols
        }
    
    def encode_categorical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Encode categorical features using Label Encoding.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with encoded categorical features
        """
        df_encoded = df.copy()
        
        for col in self.categorical_columns:
            if col in df_encoded.columns:
                le = LabelEncoder()
                # Handle missing values
                df_encoded[col] = df_encoded[col].fillna('missing')
                df_encoded[col] = le.fit_transform(df_encoded[col].astype(str))
                self.label_encoders[col] = le
                
        return df_encoded
    
    def scale_numerical_features(self, df: pd.DataFrame, fit: bool = True) -> pd.DataFrame:
        """
        Scale numerical features.

        Args:
            df: Input DataFrame
            fit: Whether to fit the scaler (True for training, False for test)

        Returns:
            DataFrame with scaled features
        """
        if not self.scale_features:
            return df

        df_scaled = df.copy()

        # Scale features (excluding target)
        feature_cols = [col for col in df.columns if col != self.target_column]

        if fit:
            if self.scaling_method == 'standard':
                self.feature_scaler = StandardScaler()
            else:
                self.feature_scaler = MinMaxScaler()

            df_scaled[feature_cols] = self.feature_scaler.fit_transform(df_scaled[feature_cols])
        else:
            if self.feature_scaler is not None:
                df_scaled[feature_cols] = self.feature_scaler.transform(df_scaled[feature_cols])

        return df_scaled
    
    def preprocess_with_group_split(self, data: pd.DataFrame, categorical_cols: List[str], 
                                     numerical_cols: List[str], group_cols: List[str], 
                                     test_size: float = 0.2, random_state: int = 42) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series]:
        """
        Preprocess data by splitting features and target, then applying scaling and encoding.
        Uses group-based splitting to ensure groups defined by group_cols don't appear in both train and test sets.
        
        Args:
            data: DataFrame containing the dataset
            categorical_cols: List of categorical column names
            numerical_cols: List of numerical column names
            group_cols: List of column names to use for group-based splitting
            test_size: Proportion of data for testing (default: 0.2)
            random_state: Random state for reproducibility (default: 42)
        
        Returns:
            X_train_proc, X_test_proc, y_train, y_test: Processed training and test sets
        """
        if data is None:
            raise ValueError("Data not provided.")
        
        X = data[categorical_cols + numerical_cols]
        y = data[self.target_column]
        
        # Create group-based splitting
        if group_cols:
            # Create unique groups based on group_cols
            groups = data[group_cols].apply(lambda row: '_'.join(row.astype(str)), axis=1)
            unique_groups = groups.unique()
            
            # Split groups into train and test
            np.random.seed(random_state)
            n_test_groups = max(1, int(len(unique_groups) * test_size))
            test_groups = np.random.choice(unique_groups, size=n_test_groups, replace=False)
            
            # Create masks for train and test based on groups
            test_mask = groups.isin(test_groups)
            train_mask = ~test_mask
            
            X_train = X[train_mask]
            X_test = X[test_mask]
            y_train = y[train_mask]
            y_test = y[test_mask]
        else:
            # Fallback to regular train_test_split if no group columns provided
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=random_state
            )
        
        self.preprocessor = ColumnTransformer([
            ('num', StandardScaler() if self.scaling_method == 'standard' else MinMaxScaler(), numerical_cols),
            ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_cols)
        ])
        
        X_train_proc = self.preprocessor.fit_transform(X_train)
        X_test_proc = self.preprocessor.transform(X_test)
        
        # Store column names after transformation for save_for_aifeynman
        self.feature_columns = numerical_cols + list(self.preprocessor.named_transformers_['cat'].get_feature_names_out(categorical_cols))

        # Convert sparse matrices to dense DataFrames and ensure y_train, y_test are Series
        X_train_proc = pd.DataFrame(X_train_proc.toarray() if hasattr(X_train_proc, "toarray") else X_train_proc, columns=self.feature_columns)
        X_test_proc = pd.DataFrame(X_test_proc.toarray() if hasattr(X_test_proc, "toarray") else X_test_proc, columns=self.feature_columns)
        
        return X_train_proc, X_test_proc, pd.Series(y_train), pd.Series(y_test)

    def preprocess(self, df: pd.DataFrame, test_size: float = 0.2, 
                   random_state: int = 42) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Complete preprocessing pipeline.
        
        Args:
            df: Input DataFrame
            test_size: Fraction of data to use for testing
            random_state: Random seed for reproducibility
            
        Returns:
            Tuple of (train_df, test_df)
        """
        print("Starting DataFrame preprocessing for AI-Feynman...")
        
        # Check if target column exists
        if self.target_column not in df.columns:
            raise ValueError(f"Target column '{self.target_column}' not found in DataFrame")
        
        # Handle missing values
        print("Handling missing values...")
        df_clean = df.copy()
        
        # For numerical columns, fill with median
        for col in df_clean.select_dtypes(include=[np.number]).columns:
            df_clean[col] = df_clean[col].fillna(df_clean[col].median())
        
        # For categorical columns, fill with mode or 'missing'
        for col in df_clean.select_dtypes(exclude=[np.number]).columns:
            if col != self.target_column:
                mode_val = df_clean[col].mode()
                fill_val = mode_val[0] if len(mode_val) > 0 else 'missing'
                df_clean[col] = df_clean[col].fillna(fill_val)
        
        # Identify column types
        print("Identifying column types...")
        col_types = self.identify_column_types(df_clean)
        print(f"Numerical columns: {col_types['numerical']}")
        print(f"Categorical columns: {col_types['categorical']}")
        
        # Encode categorical features
        if self.categorical_columns:
            print("Encoding categorical features...")
            df_encoded = self.encode_categorical_features(df_clean)
        else:
            df_encoded = df_clean.copy()
        
        # Scale features
        print("Scaling features...")
        df_scaled = self.scale_numerical_features(df_encoded, fit=True)
        
        # Split into train and test
        if test_size > 0:
            print(f"Splitting data: {1-test_size:.1%} train, {test_size:.1%} test")
            train_df, test_df = train_test_split(
                df_scaled, test_size=test_size, random_state=random_state
            )
        else:
            train_df = df_scaled
            test_df = pd.DataFrame()
        
        # Store feature columns for later use
        self.feature_columns = [col for col in train_df.columns if col != self.target_column]
        
        print(f"Preprocessing completed!")
        print(f"Train set shape: {train_df.shape}")
        if not test_df.empty:
            print(f"Test set shape: {test_df.shape}")
        
        return train_df, test_df
    
    def save_for_aifeynman(self, df: pd.DataFrame, filepath: str, 
                          variable_names: Optional[List[str]] = None) -> str:
        """
        Save the processed DataFrame to a space-separated text file for AI-Feynman.
        Also saves a list of variable names if provided.

        Args:
            df: DataFrame to save (should contain only numerical data, including target)
            filepath: Path to save the data file
            variable_names: Optional list of variable names. If provided, a
                            corresponding <filepath>_variables.txt will be saved.

        Returns:
            The path to the saved data file.
        """
        if df.empty:
            print(f"Warning: DataFrame is empty. Skipping save for {filepath}")
            return ""

        # Ensure all data is numeric, try to convert if not, warn if issues
        for col in df.columns:
            if not pd.api.types.is_numeric_dtype(df[col]):
                try:
                    df[col] = pd.to_numeric(df[col])
                except ValueError:
                    print(f"Warning: Column {col} could not be converted to numeric and might cause issues for AI-Feynman.")

        # AI-Feynman expects space-separated values, no index, no header in the main data file
        # Explicitly handle NaNs with na_rep='NaN'
        df.to_csv(filepath, sep=' ', index=False, header=False, float_format='%.10g', na_rep='NaN')

        if variable_names:
            var_filepath = filepath.replace(".txt", "_variables.txt")
            with open(var_filepath, 'w') as var_file:
                var_file.write('\n'.join(variable_names))
            print(f"Variable names saved to: {var_filepath}")
        
        print(f"Data saved successfully: {df.shape[0]} rows, {df.shape[1]} columns")
        return filepath
    
    def get_preprocessing_summary(self) -> Dict:
        """
        Get summary of preprocessing steps applied.
        
        Returns:
            Dictionary with preprocessing information
        """
        return {
            'target_column': self.target_column,
            'numerical_columns': self.numerical_columns,
            'categorical_columns': self.categorical_columns,
            'feature_columns': self.feature_columns,
            'scaling_method': self.scaling_method if self.scale_features else 'none',
            'label_encoders': list(self.label_encoders.keys()),
            'total_features': len(self.feature_columns)
        }


def create_sample_dataset() -> pd.DataFrame:
    """
    Create a sample dataset with numerical and categorical features.
    
    Returns:
        Sample DataFrame
    """
    np.random.seed(42)
    n_samples = 1000
    
    # Numerical features
    x1 = np.random.uniform(1, 5, n_samples)
    x2 = np.random.uniform(0, 3, n_samples)
    x3 = np.random.normal(2, 1, n_samples)
    
    # Categorical features
    category_A = np.random.choice(['Type1', 'Type2', 'Type3'], n_samples)
    category_B = np.random.choice(['Small', 'Medium', 'Large'], n_samples)
    
    # Target variable (some complex relationship)
    # y = x1^2 + x2*x3 + noise + categorical effects
    cat_A_effect = np.where(category_A == 'Type1', 0, 
                           np.where(category_A == 'Type2', 1, 2))
    cat_B_effect = np.where(category_B == 'Small', 0,
                           np.where(category_B == 'Medium', 0.5, 1))
    
    y = x1**2 + x2*x3 + cat_A_effect + cat_B_effect + np.random.normal(0, 0.1, n_samples)
    
    # Create DataFrame
    df = pd.DataFrame({
        'feature_1': x1,
        'feature_2': x2,
        'feature_3': x3,
        'category_A': category_A,
        'category_B': category_B,
        'target': y
    })
    
    return df


if __name__ == "__main__":
    # Example usage
    print("=== DataFrame Preprocessor for AI-Feynman ===\n")
    
    # Create sample dataset
    print("Creating sample dataset...")
    df = create_sample_dataset()
    print(f"Sample dataset shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print(f"Data types:\n{df.dtypes}")
    print(f"\nFirst 5 rows:")
    print(df.head())
    
    # Initialize preprocessor
    print("\n" + "="*50)
    print("Preprocessing dataset...")
    preprocessor = DataFramePreprocessor(
        target_column='target',
        scale_features=True,
        scaling_method='standard'
    )
    
    # Preprocess data
    train_df, test_df = preprocessor.preprocess(df, test_size=0.2)
    
    # Save for AI-Feynman
    os.makedirs('processed_data', exist_ok=True)
    train_file = preprocessor.save_for_aifeynman(
        train_df, 
        'processed_data/train_data.txt',
        variable_names=['feature_1', 'feature_2', 'feature_3', 'category_A_encoded', 'category_B_encoded', 'target']
    )
    
    if not test_df.empty:
        test_file = preprocessor.save_for_aifeynman(
            test_df, 
            'processed_data/test_data.txt'
        )
    
    # Print summary
    print("\n" + "="*50)
    print("Preprocessing Summary:")
    summary = preprocessor.get_preprocessing_summary()
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    print(f"\nFiles ready for AI-Feynman:")
    print(f"- Training data: {train_file}")
    if not test_df.empty:
        print(f"- Test data: {test_file}")
