#!/usr/bin/env python3
"""
Complete AI-Feynman Workflow Example

This script demonstrates the complete workflow from raw DataFrame
to symbolic regression results using AI-Feynman.

Author: AI Assistant
"""

import sys
import os
import time
import pandas as pd
import numpy as np
sys.path.append('./<PERSON>-<PERSON>ynman')

from sklearn.compose import ColumnTransformer
from dataframe_preprocessor import DataFramePreprocessor
from sklearn.preprocessing import OneHotEncoder, StandardScaler
from sklearn.model_selection import train_test_split


import aifeynman


BF_TRY_TIME = 45
NN_EPOCHS = 150
POLYFIT_DEG = 3

DATA_FILE = 'data.csv'

SCALE_FEATURES = True
SCALING_METHOD ='standard'

VARIABLE_NAMES = [
    'DNAcontent', 'amu', 'Sim_Mean_Dose_Gy', 'Std_Dose_Gy',
    'Mean_Tracks_per_Nucleus', 'hit_cells_ratio', 'no_hit_ratio',
    'mean_dose_hitCells', 'Std_Dose_hitCells_Gy', 'SFi'
]

CATEGORICAL_COLS = ['CellClass', 'CellCycle', 'IrradiationConditions']
NUMERICAL_COLS = ['DNAcontent', 'amu', 'Sim_Mean_Dose_Gy', 'Std_Dose_Gy',
                  'Mean_Tracks_per_Nucleus', 'hit_cells_ratio', 'no_hit_ratio',
                  'mean_dose_hitCells', 'Std_Dose_hitCells_Gy']
TARGET_COLUMN = 'SFi'


GROUP_COLS = ['amu']
TEST_SIZE = 0.20
RANDOM_STATE = 42


def create_physics_dataset():
    """Create a dataset based on a physics relationship."""
    print("Creating physics-inspired dataset...")
    print("Relationship: F = m*a + friction_coefficient*m*g")
    
    np.random.seed(42)
    n_samples = 200
    
    # Physical parameters
    mass = np.random.uniform(1, 10, n_samples)  # kg
    acceleration = np.random.uniform(0, 5, n_samples)  # m/s²
    friction_coeff = np.random.uniform(0.1, 0.5, n_samples)
    g = 9.81  # gravity constant
    
    # Categorical variables
    surface_type = np.random.choice(['Smooth', 'Rough', 'Very_Rough'], n_samples, p=[0.3, 0.5, 0.2])
    material = np.random.choice(['Steel', 'Aluminum', 'Plastic'], n_samples, p=[0.4, 0.4, 0.2])
    
    # Adjust friction based on surface type
    surface_multiplier = np.where(surface_type == 'Smooth', 0.8,
                                np.where(surface_type == 'Rough', 1.0, 1.3))
    friction_coeff *= surface_multiplier
    
    # Adjust mass based on material
    material_density = np.where(material == 'Steel', 1.2,
                              np.where(material == 'Aluminum', 1.0, 0.7))
    mass *= material_density
    
    # Calculate force with some noise
    force = mass * acceleration + friction_coeff * mass * g
    force += np.random.normal(0, 0.5, n_samples)  # measurement noise
    
    # Create DataFrame
    df = pd.DataFrame({
        'mass_kg': mass,
        'acceleration_ms2': acceleration,
        'friction_coefficient': friction_coeff,
        'surface_type': surface_type,
        'material': material,
        'force_N': force
    })
    
    print(f"Dataset created: {df.shape}")
    print(f"True relationship: F = m*a + μ*m*g")
    print(f"Where μ depends on surface_type and material affects mass")
    
    return df

def preprocess_data(data, categorical_cols, numerical_cols, target_col, group_cols, test_size=0.2, random_state=42):
    """
    Preprocess data by splitting features and target, then applying scaling and encoding.
    Uses group-based splitting to ensure groups defined by group_cols don't appear in both train and test sets.
    
    Args:
        data: DataFrame containing the dataset
        categorical_cols: List of categorical column names
        numerical_cols: List of numerical column names
        target_col: Name of the target column
        group_cols: List of column names to use for group-based splitting
        test_size: Proportion of data for testing (default: 0.2)
        random_state: Random state for reproducibility (default: 42)
    
    Returns:
        X_train_proc, X_test_proc, y_train, y_test: Processed training and test sets
    """
    if data is None:
        raise ValueError("Data not provided.")
    
    X = data[categorical_cols + numerical_cols]
    y = data[target_col]
    
    # Create group-based splitting
    if group_cols:
        # Create unique groups based on group_cols
        groups = data[group_cols].apply(lambda row: '_'.join(row.astype(str)), axis=1)
        unique_groups = groups.unique()
        
        # Split groups into train and test
        np.random.seed(random_state)
        n_test_groups = max(1, int(len(unique_groups) * test_size))
        test_groups = np.random.choice(unique_groups, size=n_test_groups, replace=False)
        
        # Create masks for train and test based on groups
        test_mask = groups.isin(test_groups)
        train_mask = ~test_mask
        
        X_train = X[train_mask]
        X_test = X[test_mask]
        y_train = y[train_mask]
        y_test = y[test_mask]
    else:
        # Fallback to regular train_test_split if no group columns provided
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )
    
    preprocessor = ColumnTransformer([
        ('num', StandardScaler(), numerical_cols),
        ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_cols)
    ])
    
    X_train_proc = preprocessor.fit_transform(X_train)
    X_test_proc = preprocessor.transform(X_test)
    
    return X_train_proc, X_test_proc, y_train, y_test


def load_data(DATA_FILE):
    print (f"Loading data from {DATA_FILE}")
    if not os.path.exists(DATA_FILE):
        raise FileNotFoundError(f"Data file {DATA_FILE} not found.")
    data = pd.read_csv(DATA_FILE)
    return data



def demonstrate_complete_workflow():
    """Demonstrate the complete workflow."""
    print("="*70)
    print("COMPLETE AI-FEYNMAN WORKFLOW DEMONSTRATION")
    print("="*70)
    
    # Step 1: Import dataset
    df = load_data(DATA_FILE)
    
    print(f"\nDataset overview:")
    print(df.head())
    print(f"\nData types:")
    print(df.dtypes)
    print(f"\nStatistical summary:")
    print(df.describe())
    
    # Step 2: Preprocess data
    print("\n\n2. PREPROCESSING DATA")
    print("-" * 30)
    
    # Initialize DataFramePreprocessor
    preprocessor = DataFramePreprocessor(target_column=TARGET_COLUMN)

    train_df, test_df, y_train, y_test = preprocessor.preprocess_with_group_split(
        df, CATEGORICAL_COLS, NUMERICAL_COLS, GROUP_COLS, 
        test_size=TEST_SIZE, random_state=RANDOM_STATE
    )
    
    # Define the actual variable names AFTER preprocessing for AI Feynman
    actual_aifeynman_variable_names = list(train_df.columns) + [TARGET_COLUMN]

    #print(f"\\nPreprocessing summary:")
    #summary = preprocessor.get_preprocessing_summary()
    #for key, value in summary.items():
    #    print(f"  {key}: {value}")
    
    # Step 3: Save for AI-Feynman
    print("\n\n3. SAVING DATA FOR AI-FEYNMAN")
    print("-" * 30)
    
    os.makedirs('physics_data', exist_ok=True)

    # Concatenate features and target for saving
    train_df_to_save = pd.concat([train_df, y_train.rename(TARGET_COLUMN)], axis=1)
    test_df_to_save = pd.concat([test_df, y_test.rename(TARGET_COLUMN)], axis=1)

    train_file = preprocessor.save_for_aifeynman(
        train_df_to_save,
        'physics_data/physics_train.txt',
        variable_names=actual_aifeynman_variable_names # Use actual processed variable names
    )
    
    test_file = preprocessor.save_for_aifeynman(
        test_df_to_save,
        'physics_data/physics_test.txt'
    )
    
    print(f"Files saved:")
    print(f"  Training: {train_file}")
    print(f"  Test: {test_file}")
    print(f"  Variables: physics_data/physics_train_variables.txt")
    
    # Step 4: Run AI-Feynman
    print("\n\n4. RUNNING AI-FEYNMAN")
    print("-" * 30)
    
    print("Parameters:")
    print(f"  BF try time: {BF_TRY_TIME} seconds")
    print(f"  NN epochs: {NN_EPOCHS}")
    print(f"  Polynomial degree: {POLYFIT_DEG}")
    print(f"  Variables: {actual_aifeynman_variable_names}") # Print the actual variables being used
    
    try:
        print(f"\\nStarting AI-Feynman symbolic regression...")
        start_time = time.time()
        
        aifeynman.run_aifeynman(
            pathdir="physics_data/",
            filename="physics_train.txt",
            BF_try_time=BF_TRY_TIME,
            BF_ops_file_type="14ops.txt",
            polyfit_deg=POLYFIT_DEG,
            NN_epochs=NN_EPOCHS,
            vars_name=actual_aifeynman_variable_names # Pass actual processed variable names to AI Feynman
        )
        
        end_time = time.time()
        print(f"\n✓ AI-Feynman completed in {end_time - start_time:.2f} seconds!")
        
        # Step 5: Analyze results
        print("\n\n5. ANALYZING RESULTS")
        print("-" * 30)
        
        results_dir = "results"
        if os.path.exists(results_dir):
            # Look for physics-related results
            physics_files = [f for f in os.listdir(results_dir) if 'physics' in f]
            
            if physics_files:
                print(f"✓ Found {len(physics_files)} result files:")
                for file in physics_files:
                    print(f"  - {file}")
                    
                # Try to read solution files
                for file in physics_files:
                    if file.startswith('solution_'):
                        solution_path = os.path.join(results_dir, file)
                        try:
                            with open(solution_path, 'r') as f:
                                content = f.read().strip()
                                if content:
                                    print(f"\n✓ Solution in {file}:")
                                    print(f"  {content}")
                        except:
                            pass
            
            # Check for separability results
            separable_dir = os.path.join(results_dir, "separable_add")
            if os.path.exists(separable_dir):
                sep_files = os.listdir(separable_dir)
                if any('physics' in f for f in sep_files):
                    print(f"\n✓ Additive separability detected!")
                    print(f"  AI-Feynman found that the relationship can be decomposed")
                    print(f"  into additive components.")
            
            # Check mystery worlds
            mystery_dirs = [d for d in os.listdir(results_dir) if d.startswith('mystery_world_')]
            if mystery_dirs:
                print(f"\n✓ Explored {len(mystery_dirs)} mathematical transformations:")
                for d in mystery_dirs[:5]:  # Show first 5
                    transform = d.replace('mystery_world_', '')
                    print(f"  - {transform}")
                if len(mystery_dirs) > 5:
                    print(f"  ... and {len(mystery_dirs) - 5} more")
        
        return True
        
    except Exception as e:
        print(f"\n✗ Error running AI-Feynman: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_preprocessing_effects():
    """Analyze the effects of different preprocessing options."""
    print("\n\n6. PREPROCESSING COMPARISON")
    print("-" * 30)
    
    # Create simple dataset
    df = create_physics_dataset()
    
    # Test different preprocessing options
    options = [
        {'scale_features': False, 'scaling_method': 'none'},
        {'scale_features': True, 'scaling_method': 'standard'},
        {'scale_features': True, 'scaling_method': 'minmax'}
    ]
    
    for i, opts in enumerate(options, 1):
        print(f"\nOption {i}: {opts}")
        
        preprocessor = DataFramePreprocessor(
            target_column='SFi',
            **opts
        )
        
        train_df, _ = preprocessor.preprocess(df, test_size=0.2)
        
        # Show statistics
        print(f"  Feature ranges after preprocessing:")
        feature_cols = [col for col in train_df.columns if col != 'force_N']
        for col in feature_cols[:3]:  # Show first 3 features
            min_val = train_df[col].min()
            max_val = train_df[col].max()
            print(f"    {col}: [{min_val:.3f}, {max_val:.3f}]")


def main():
    """Main function."""
    print("AI-FEYNMAN COMPLETE WORKFLOW EXAMPLE")
    print("="*70)
    print("This example demonstrates:")
    print("1. Creating a physics-inspired dataset with mixed data types")
    print("2. Preprocessing the data for AI-Feynman")
    print("3. Running symbolic regression")
    print("4. Analyzing the results")
    print("5. Comparing preprocessing options")
    print("="*70)
    
    # Run the complete workflow
    success = demonstrate_complete_workflow()
    
    # Analyze preprocessing effects
    analyze_preprocessing_effects()
    
    # Final summary
    print("\n\n" + "="*70)
    print("WORKFLOW SUMMARY")
    print("="*70)
    print(f"✓ Dataset creation: Success")
    print(f"✓ Data preprocessing: Success")
    print(f"✓ AI-Feynman execution: {'Success' if success else 'Failed'}")
    print(f"✓ Results analysis: Success")
    
    print(f"\nGenerated files:")
    files_to_check = [
        'physics_data/physics_train.txt',
        'physics_data/physics_test.txt',
        'physics_data/physics_train_variables.txt'
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  ✓ {file} ({size} bytes)")
        else:
            print(f"  ✗ {file} (not found)")
    
    if os.path.exists('results'):
        result_count = len([f for f in os.listdir('results') if 'physics' in f])
        print(f"  ✓ {result_count} result files in results/ directory")
    
    print(f"\n" + "="*70)
    print("NEXT STEPS:")
    print("1. Examine the results/ directory for discovered equations")
    print("2. Try different datasets with your own data")
    print("3. Experiment with different AI-Feynman parameters")
    print("4. Use the DataFramePreprocessor for your own projects")
    print("="*70)


if __name__ == "__main__":
    main()
