#!/usr/bin/env python3
"""
Complete AI-Feynman Example with DataFrame Preprocessing

This script demonstrates:
1. Running AI-Feynman on the provided example data
2. Using the DataFrame preprocessor to prepare custom data for AI-Feynman
3. Running AI-Feynman on the preprocessed custom data

Author: AI Assistant
"""

import sys
import os
import time
sys.path.append('./AI-Feynman')

import pandas as pd
import numpy as np
from dataframe_preprocessor import DataFramePreprocessor, create_sample_dataset

# Import AI-Feynman
import aifeynman


def run_aifeynman_on_example():
    """Run AI-Feynman on the provided example data."""
    print("="*60)
    print("RUNNING AI-FEYNMAN ON EXAMPLE DATA")
    print("="*60)
    
    # Parameters for AI-Feynman
    pathdir = "./AI-Feynman/example_data/"
    filename = "example1.txt"
    BF_try_time = 30  # Reduced time for faster testing
    BF_ops_file_type = "14ops.txt"
    polyfit_deg = 3
    NN_epochs = 100  # Reduced epochs for faster testing
    
    print(f"Data file: {pathdir}{filename}")
    print(f"BF try time: {BF_try_time} seconds")
    print(f"Operations file: {BF_ops_file_type}")
    print(f"Polynomial degree: {polyfit_deg}")
    print(f"NN epochs: {NN_epochs}")
    
    try:
        print("\nStarting AI-Feynman...")
        start_time = time.time()
        
        # Run AI-Feynman
        aifeynman.run_aifeynman(
            pathdir=pathdir,
            filename=filename,
            BF_try_time=BF_try_time,
            BF_ops_file_type=BF_ops_file_type,
            polyfit_deg=polyfit_deg,
            NN_epochs=NN_epochs
        )
        
        end_time = time.time()
        print(f"\n✓ AI-Feynman completed successfully in {end_time - start_time:.2f} seconds!")
        
        # Check for results
        results_dir = "results"
        if os.path.exists(results_dir):
            result_files = os.listdir(results_dir)
            print(f"✓ Results saved in '{results_dir}' directory:")
            for file in result_files:
                print(f"  - {file}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error running AI-Feynman: {e}")
        import traceback
        traceback.print_exc()
        return False


def demonstrate_dataframe_preprocessing():
    """Demonstrate the DataFrame preprocessing utility."""
    print("\n" + "="*60)
    print("DEMONSTRATING DATAFRAME PREPROCESSING")
    print("="*60)
    
    # Create sample dataset
    print("Creating sample dataset with numerical and categorical features...")
    df = create_sample_dataset()
    
    print(f"Dataset shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print(f"\nData types:")
    print(df.dtypes)
    print(f"\nFirst 5 rows:")
    print(df.head())
    
    print(f"\nSample statistics:")
    print(df.describe())
    
    # Initialize preprocessor
    print("\n" + "-"*40)
    print("Initializing preprocessor...")
    preprocessor = DataFramePreprocessor(
        target_column='target',
        scale_features=True,
        scaling_method='standard'
    )
    
    # Preprocess data
    print("Preprocessing data...")
    train_df, test_df = preprocessor.preprocess(df, test_size=0.2)
    
    print(f"\nProcessed data shapes:")
    print(f"Training set: {train_df.shape}")
    print(f"Test set: {test_df.shape}")
    
    print(f"\nProcessed training data (first 5 rows):")
    print(train_df.head())
    
    # Save for AI-Feynman
    print("\n" + "-"*40)
    print("Saving data for AI-Feynman...")
    os.makedirs('processed_data', exist_ok=True)
    
    variable_names = ['feature_1', 'feature_2', 'feature_3', 'category_A_encoded', 'category_B_encoded', 'target']
    train_file = preprocessor.save_for_aifeynman(
        train_df, 
        'processed_data/train_data.txt',
        variable_names=variable_names
    )
    
    test_file = preprocessor.save_for_aifeynman(
        test_df, 
        'processed_data/test_data.txt'
    )
    
    # Print preprocessing summary
    print("\n" + "-"*40)
    print("Preprocessing Summary:")
    summary = preprocessor.get_preprocessing_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    return train_file, test_file, variable_names


def run_aifeynman_on_custom_data(data_file, variable_names):
    """Run AI-Feynman on custom preprocessed data."""
    print("\n" + "="*60)
    print("RUNNING AI-FEYNMAN ON CUSTOM PREPROCESSED DATA")
    print("="*60)
    
    # Parameters for AI-Feynman
    pathdir = "processed_data/"
    filename = os.path.basename(data_file)
    BF_try_time = 30
    BF_ops_file_type = "14ops.txt"
    polyfit_deg = 3
    NN_epochs = 100
    
    print(f"Data file: {pathdir}{filename}")
    print(f"Variables: {variable_names}")
    print(f"BF try time: {BF_try_time} seconds")
    
    try:
        print("\nStarting AI-Feynman on custom data...")
        start_time = time.time()
        
        # Run AI-Feynman
        aifeynman.run_aifeynman(
            pathdir=pathdir,
            filename=filename,
            BF_try_time=BF_try_time,
            BF_ops_file_type=BF_ops_file_type,
            polyfit_deg=polyfit_deg,
            NN_epochs=NN_epochs,
            vars_name=variable_names
        )
        
        end_time = time.time()
        print(f"\n✓ AI-Feynman completed on custom data in {end_time - start_time:.2f} seconds!")
        
        # Check for results
        results_dir = "results"
        if os.path.exists(results_dir):
            result_files = [f for f in os.listdir(results_dir) if filename.replace('.txt', '') in f]
            if result_files:
                print(f"✓ Results for custom data:")
                for file in result_files:
                    print(f"  - {file}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error running AI-Feynman on custom data: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function to run the complete example."""
    print("AI-FEYNMAN COMPLETE EXAMPLE")
    print("="*60)
    print("This example demonstrates:")
    print("1. Running AI-Feynman on provided example data")
    print("2. Preprocessing a DataFrame with numerical and categorical data")
    print("3. Running AI-Feynman on the preprocessed custom data")
    print("="*60)
    
    # Step 1: Run AI-Feynman on example data
    success1 = run_aifeynman_on_example()
    
    # Step 2: Demonstrate DataFrame preprocessing
    train_file, test_file, variable_names = demonstrate_dataframe_preprocessing()
    
    # Step 3: Run AI-Feynman on custom data
    success2 = run_aifeynman_on_custom_data(train_file, variable_names)
    
    # Summary
    print("\n" + "="*60)
    print("EXAMPLE SUMMARY")
    print("="*60)
    print(f"✓ Example data processing: {'Success' if success1 else 'Failed'}")
    print(f"✓ DataFrame preprocessing: Success")
    print(f"✓ Custom data processing: {'Success' if success2 else 'Failed'}")
    
    print(f"\nFiles created:")
    print(f"  - {train_file}")
    print(f"  - {test_file}")
    print(f"  - processed_data/train_data_variables.txt")
    
    if os.path.exists("results"):
        result_files = os.listdir("results")
        if result_files:
            print(f"\nAI-Feynman results:")
            for file in result_files:
                print(f"  - results/{file}")
    
    print(f"\n{'='*60}")
    print("Example completed! You can now use the DataFramePreprocessor")
    print("class to prepare your own datasets for AI-Feynman.")
    print("="*60)


if __name__ == "__main__":
    main()
